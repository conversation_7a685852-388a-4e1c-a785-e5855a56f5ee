#!/usr/bin/env python3
"""
简化版查询规划智能体测试
验证基本功能和意图分析
"""

import asyncio
import uuid
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.query_planner_agent import QueryPlannerAgent
from src.agent.data_models import QueryPlannerTask


async def test_intent_analysis_only():
    """测试意图分析功能"""
    print("\n=== 测试意图分析功能 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 创建测试任务
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="找一些圆形的零件",
            top_k=10
        )
        
        # 直接测试意图分析
        intent_analysis = await agent._analyze_query_intent(task)
        
        print("意图分析结果:")
        print(f"主要查询对象: {intent_analysis.main_query_object}")
        print(f"形状几何约束: {intent_analysis.shape_geometry_constraints}")
        print(f"属性约束: {intent_analysis.attribute_constraints}")
        print(f"语义描述: {intent_analysis.semantic_descriptions}")
        print(f"结构关系约束: {intent_analysis.structural_relationship_constraints}")
        print(f"包含形状向量: {intent_analysis.has_shape_vector}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_complex_intent_analysis():
    """测试复杂查询的意图分析"""
    print("\n=== 测试复杂查询意图分析 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 创建复杂查询任务
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="给我找跟上传的模型差不多的泵阀零件，材料得用304不锈钢，而且要直接用法兰接到离心泵",
            shape_vector=[0.1] * 768,
            top_k=15
        )
        
        # 测试意图分析
        intent_analysis = await agent._analyze_query_intent(task)
        
        print("复杂查询意图分析结果:")
        print(f"主要查询对象: {intent_analysis.main_query_object}")
        print(f"形状几何约束: {intent_analysis.shape_geometry_constraints}")
        print(f"属性约束: {intent_analysis.attribute_constraints}")
        print(f"语义描述: {intent_analysis.semantic_descriptions}")
        print(f"结构关系约束: {intent_analysis.structural_relationship_constraints}")
        print(f"包含形状向量: {intent_analysis.has_shape_vector}")
        
        # 验证分析结果
        assert intent_analysis.main_query_object in ["零件", "泵阀零件", "零件或装配体"]
        assert intent_analysis.has_shape_vector == True
        assert len(intent_analysis.attribute_constraints) > 0
        assert len(intent_analysis.semantic_descriptions) > 0
        
        print("✓ 复杂查询意图分析验证通过")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_result_fusion_strategy():
    """测试结果融合策略生成"""
    print("\n=== 测试结果融合策略 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 创建任务
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="找材料为钢的圆形零件",
            top_k=10
        )
        
        # 先分析意图
        intent_analysis = await agent._analyze_query_intent(task)
        
        # 生成融合策略
        result_fusion = await agent._define_result_fusion(task, intent_analysis)
        
        print("结果融合策略:")
        print(f"合并方法: {result_fusion.merge_method}")
        print(f"排序标准: {result_fusion.ranking_criteria}")
        print(f"描述: {result_fusion.description}")
        
        # 验证融合策略
        assert result_fusion.merge_method in ["intersection", "union", "weighted"]
        assert len(result_fusion.ranking_criteria) > 0
        
        print("✓ 结果融合策略验证通过")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_basic_query_plan_generation():
    """测试基础查询计划生成"""
    print("\n=== 测试基础查询计划生成 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 使用便捷方法生成查询计划
        query_plan = await agent.get_query_plan_only(
            query_text="找一些圆形的零件",
            top_k=10
        )
        
        if query_plan:
            print("查询计划生成成功:")
            print(f"计划ID: {query_plan.plan_id}")
            print(f"查询文本: {query_plan.query_text}")
            print(f"步骤数量: {len(query_plan.steps)}")
            
            # 验证查询计划结构
            assert query_plan.plan_id is not None
            assert query_plan.query_text == "找一些圆形的零件"
            assert len(query_plan.steps) > 0
            assert query_plan.intent_analysis is not None
            assert query_plan.result_fusion is not None
            
            print("✓ 查询计划结构验证通过")
            return True
        else:
            print("查询计划生成失败")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_execute_task_method():
    """测试execute_task方法"""
    print("\n=== 测试execute_task方法 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 创建查询任务
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="找一些圆形的零件",
            top_k=10
        )
        
        # 执行任务
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        print(f"结果数量: {result.total_results}")
        
        if result.status == 'success':
            assert len(result.results) == 1
            assert result.results[0].search_type == "query_plan"
            assert "plan_id" in result.results[0].metadata
            
            print("✓ execute_task方法验证通过")
            return True
        else:
            print(f"任务执行失败: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        await agent.disconnect()


async def main():
    """主测试函数"""
    print("🚀 开始简化版查询规划智能体测试...")
    
    test_results = []
    
    # 测试意图分析
    try:
        result = await test_intent_analysis_only()
        test_results.append(("意图分析", result))
    except Exception as e:
        print(f"意图分析测试异常: {e}")
        test_results.append(("意图分析", False))
    
    # 测试复杂意图分析
    try:
        result = await test_complex_intent_analysis()
        test_results.append(("复杂意图分析", result))
    except Exception as e:
        print(f"复杂意图分析测试异常: {e}")
        test_results.append(("复杂意图分析", False))
    
    # 测试结果融合策略
    try:
        result = await test_result_fusion_strategy()
        test_results.append(("结果融合策略", result))
    except Exception as e:
        print(f"结果融合策略测试异常: {e}")
        test_results.append(("结果融合策略", False))
    
    # 测试基础查询计划生成
    try:
        result = await test_basic_query_plan_generation()
        test_results.append(("基础查询计划生成", result))
    except Exception as e:
        print(f"基础查询计划生成测试异常: {e}")
        test_results.append(("基础查询计划生成", False))
    
    # 测试execute_task方法
    try:
        result = await test_execute_task_method()
        test_results.append(("execute_task方法", result))
    except Exception as e:
        print(f"execute_task方法测试异常: {e}")
        test_results.append(("execute_task方法", False))
    
    # 输出测试结果汇总
    print("\n" + "="*50)
    print("🎯 测试结果汇总")
    print("="*50)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总计: {success_count}/{len(test_results)} 个测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！查询规划智能体基本功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    asyncio.run(main())
