"""
查询规划智能体 (Query Planner Agent)
分析用户的自然语言查询，生成结构化的查询计划
"""

import logging
import json
import uuid
import time
from typing import List, Dict, Any, Optional
from src.agent.base_agent import BaseAgent
from src.agent.data_models import (
    QueryResult, QueryPlannerTask, SearchResultItem, QueryPlan,
    IntentAnalysis, QueryStep, ResultFusion
)
from src.models.LLM import MultiProviderLLM
from src.config import Config

logger = logging.getLogger(__name__)


class QueryPlannerAgent(BaseAgent):
    """
    查询规划智能体
    
    该智能体负责分析用户的自然语言查询，识别查询意图和约束条件，
    生成结构化的查询计划，指定需要调用的专业智能体及其参数，
    并定义查询步骤之间的依赖关系和结果融合策略。
    """
    
    def __init__(self, agent_name: str = "QueryPlannerAgent"):
        super().__init__(agent_name)
        self.llm = None
        self._connected = False
        
    async def connect(self):
        """连接到LLM服务"""
        try:
            if not self._connected:
                self.llm = MultiProviderLLM()
                self._connected = True
                logger.info(f"{self.agent_name} 已连接到LLM服务")
        except Exception as e:
            logger.error(f"{self.agent_name} 连接LLM服务失败: {e}")
            raise
    
    async def disconnect(self):
        """断开连接"""
        if self._connected:
            self.llm = None
            self._connected = False
            logger.info(f"{self.agent_name} 已断开连接")
    
    async def execute_task(self, task: QueryPlannerTask) -> QueryResult:
        """
        执行查询规划任务
        
        Args:
            task: QueryPlannerTask实例，包含用户查询文本和可选的形状向量
            
        Returns:
            QueryResult: 包含查询计划的结果对象
        """
        start_time = time.time()
        
        try:
            if not self._connected:
                await self.connect()
            
            # 生成查询计划
            query_plan = await self._generate_query_plan(task)
            
            # 将查询计划包装为SearchResultItem
            search_result = SearchResultItem(
                rank=1,
                uuid=query_plan.plan_id,
                name=f"查询计划_{query_plan.plan_id[:8]}",
                description=f"针对查询'{task.query_text}'的执行计划",
                search_type="query_plan",
                metadata=query_plan.model_dump()
            )
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=[search_result],
                execution_time=execution_time,
                total_results=1
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"查询规划失败: {e}")
            
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[],
                execution_time=execution_time,
                total_results=0
            )
    
    async def _generate_query_plan(self, task: QueryPlannerTask) -> QueryPlan:
        """
        生成查询计划
        
        Args:
            task: 查询规划任务
            
        Returns:
            QueryPlan: 生成的查询计划
        """
        plan_id = str(uuid.uuid4())
        
        # 1. 分析查询意图
        intent_analysis = await self._analyze_query_intent(task)
        
        # 2. 生成查询步骤
        steps = await self._generate_query_steps(task, intent_analysis)
        
        # 3. 定义结果融合策略
        result_fusion = await self._define_result_fusion(task, intent_analysis)
        
        return QueryPlan(
            plan_id=plan_id,
            query_text=task.query_text,
            intent_analysis=intent_analysis.model_dump(),
            steps=[step.model_dump() for step in steps],
            result_fusion=result_fusion.model_dump()
        )
    
    async def _analyze_query_intent(self, task: QueryPlannerTask) -> IntentAnalysis:
        """
        分析查询意图
        
        Args:
            task: 查询规划任务
            
        Returns:
            IntentAnalysis: 查询意图分析结果
        """
        system_prompt = self._build_intent_analysis_prompt()
        
        user_prompt = f"""
请分析以下用户查询的意图：

用户查询: {task.query_text}
是否包含形状向量: {'是' if task.shape_vector else '否'}

请按照JSON格式返回分析结果：
{{
    "main_query_object": "主要查询对象（如：零件、装配体、组件等）",
    "shape_geometry_constraints": ["形状几何约束列表"],
    "attribute_constraints": {{"属性名": "约束条件"}},
    "semantic_descriptions": ["语义描述列表"],
    "structural_relationship_constraints": ["结构关系约束列表"],
    "has_shape_vector": {str(task.shape_vector is not None).lower()}
}}
"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm.chat(messages)
        
        try:
            # 清理响应并解析JSON
            cleaned_response = self._clean_json_response(response)
            intent_data = json.loads(cleaned_response)
            
            return IntentAnalysis(**intent_data)
            
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"意图分析响应解析失败，使用默认值: {e}")
            return IntentAnalysis(
                main_query_object="零件或装配体",
                has_shape_vector=task.shape_vector is not None
            )
    
    async def _generate_query_steps(self, task: QueryPlannerTask, intent: IntentAnalysis) -> List[QueryStep]:
        """
        生成查询步骤
        
        Args:
            task: 查询规划任务
            intent: 查询意图分析结果
            
        Returns:
            List[QueryStep]: 查询步骤列表
        """
        system_prompt = self._build_step_generation_prompt()
        
        user_prompt = f"""
基于以下查询意图分析，生成查询步骤：

原始查询: {task.query_text}
意图分析: {json.dumps(intent.model_dump(), ensure_ascii=False, indent=2)}

请按照JSON数组格式返回查询步骤：
[
    {{
        "id": "step1",
        "step_name": "步骤名称",
        "agent": "智能体类型（StructuredDataAgent/StructuralRelationshipAgent/GeometrySemanticAgent）",
        "parameters": {{"参数名": "参数值"}},
        "description": "步骤描述",
        "depends_on": null
    }}
]
"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm.chat(messages)
        
        try:
            # 清理响应并解析JSON
            cleaned_response = self._clean_json_response(response)
            steps_data = json.loads(cleaned_response)

            # 确保steps_data是列表
            if not isinstance(steps_data, list):
                raise ValueError("步骤数据应该是列表格式")

            return [QueryStep(**step) for step in steps_data]

        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"查询步骤生成失败，使用默认步骤: {e}")
            # 返回默认的单步查询
            return [QueryStep(
                id="step1",
                step_name="default_search",
                agent="GeometrySemanticAgent",
                parameters={
                    "query_text": task.query_text,
                    "shape_vector": task.shape_vector,
                    "top_k": task.top_k
                },
                description="默认语义搜索"
            )]
    
    async def _define_result_fusion(self, task: QueryPlannerTask, intent: IntentAnalysis) -> ResultFusion:
        """
        定义结果融合策略
        
        Args:
            task: 查询规划任务
            intent: 查询意图分析结果
            
        Returns:
            ResultFusion: 结果融合策略
        """
        # 根据查询意图确定融合策略
        if len(intent.attribute_constraints) > 0 and len(intent.semantic_descriptions) > 0:
            # 有属性约束和语义描述，使用交集合并
            return ResultFusion(
                merge_method="intersection",
                ranking_criteria=[
                    {"field": "similarity_score", "direction": "desc"},
                    {"field": "rank", "direction": "asc"}
                ],
                description="按相似度降序排列，优先满足所有约束条件的结果"
            )
        else:
            # 简单查询，使用联合合并
            return ResultFusion(
                merge_method="union",
                ranking_criteria=[
                    {"field": "similarity_score", "direction": "desc"}
                ],
                description="按相似度降序排列"
            )

    def _build_intent_analysis_prompt(self) -> str:
        """构建意图分析的系统提示词"""
        return """
你是一个专业的CAD/CAE查询意图分析专家。你的任务是分析用户的自然语言查询，识别其中的各种约束条件和查询意图。

## 智能体分工说明：

1. **结构化数据查询智能体(StructuredDataAgent)**：
   - 处理精确的物理属性：mass(质量), volume(体积), density(密度), length(长度), width(宽度), height(高度), area(面积)
   - 处理材料属性：material(材料类型)
   - 处理数值属性：hole_count(孔数量), part_count(零件数量)
   - 处理分类属性：industry(行业), category(类别)

2. **几何和语义查询智能体(GeometrySemanticAgent)**：
   - 处理功能描述、外形描述等文本语义信息
   - 处理形状特征向量相似性搜索
   - 支持混合搜索（文本+形状）

3. **结构关系查询智能体(StructuralRelationshipAgent)**：
   - 处理组件包含关系、装配体层次结构
   - 处理连接关系、装配约束
   - 处理BOM层级关系

## 分析要求：
- 准确识别查询中的各种约束条件
- 区分哪些约束应该由哪个智能体处理
- 识别查询的主要对象（零件、装配体、组件等）
- 提取语义描述和功能要求
- 识别结构关系约束
"""

    def _build_step_generation_prompt(self) -> str:
        """构建查询步骤生成的系统提示词"""
        return """
你是一个专业的查询规划专家。基于查询意图分析，你需要生成合理的查询步骤序列。

## 查询步骤规划原则：

1. **步骤顺序**：
   - 先执行约束性强的查询（如结构关系、精确属性）
   - 再执行语义相似性查询
   - 最后进行形状相似性查询

2. **依赖关系**：
   - 后续步骤可以使用前面步骤的结果作为ID过滤条件
   - 通过"depends_on"字段指定依赖关系
   - 通过"input_ids"参数传递前面步骤的结果

3. **智能体参数**：
   - StructuredDataAgent: query_text, id_list(可选), top_k
   - StructuralRelationshipAgent: query_text, id_list(可选), top_k
   - GeometrySemanticAgent: query_text(可选), shape_vector(可选), id_list(可选), top_k, shape_weight, use_reranker

4. **步骤命名**：
   - structural_query: 结构关系查询
   - attribute_filter: 属性过滤
   - semantic_search: 语义搜索
   - geometry_search: 几何搜索
   - hybrid_search: 混合搜索

## 输出格式：
返回JSON数组，每个步骤包含id, step_name, agent, parameters, description, depends_on字段。
"""

    def _clean_json_response(self, response: str) -> str:
        """
        清理LLM响应，提取JSON内容

        Args:
            response: LLM原始响应

        Returns:
            str: 清理后的JSON字符串
        """
        # 移除可能的markdown代码块标记
        response = response.strip()
        if response.startswith("```json"):
            response = response[7:]
        if response.startswith("```"):
            response = response[3:]
        if response.endswith("```"):
            response = response[:-3]

        response = response.strip()

        # 查找JSON内容 - 优先查找数组格式（用于步骤）
        start_idx = response.find('[')
        end_idx = response.rfind(']')

        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            json_content = response[start_idx:end_idx+1]
        else:
            # 查找对象格式
            start_idx = response.find('{')
            end_idx = response.rfind('}')
            if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
                json_content = response[start_idx:end_idx+1]
            else:
                json_content = response

        return json_content.strip()

    async def get_query_plan_only(self, query_text: str, shape_vector: Optional[List[float]] = None, top_k: int = 10) -> Optional[QueryPlan]:
        """
        仅生成查询计划，不执行查询

        Args:
            query_text: 用户查询文本
            shape_vector: 可选的形状向量
            top_k: 返回结果数量

        Returns:
            QueryPlan: 查询计划，如果失败返回None
        """
        try:
            task = QueryPlannerTask(
                task_id=str(uuid.uuid4()),
                query_text=query_text,
                shape_vector=shape_vector,
                top_k=top_k
            )

            result = await self.execute_task(task)

            if result.status == 'success' and result.results:
                return QueryPlan(**result.results[0].metadata)
            else:
                logger.error(f"查询计划生成失败: {result.error_message}")
                return None

        except Exception as e:
            logger.error(f"获取查询计划失败: {e}")
            return None
