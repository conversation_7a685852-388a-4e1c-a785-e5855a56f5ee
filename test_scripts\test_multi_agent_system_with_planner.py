#!/usr/bin/env python3
"""
测试完整的多智能体查询系统（包含查询规划智能体）
演示如何使用查询规划智能体协调三个专业查询智能体
"""

import asyncio
import uuid
import sys
import os
import json
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.query_planner_agent import QueryPlannerAgent
from src.agent.structured_data_agent import StructuredDataAgent
from src.agent.structural_relationship_agent import StructuralRelationshipAgent
from src.agent.geometry_semantic_agent import GeometrySemanticAgent
from src.agent.data_models import (
    QueryPlannerTask, UnifiedStructuredDataTask, 
    UnifiedStructuralQueryTask, UnifiedGeometrySemanticTask,
    QueryResult, SearchResultItem
)


class MultiAgentQuerySystem:
    """
    多智能体查询系统
    
    集成查询规划智能体和三个专业查询智能体，
    实现复杂查询的自动分解和执行
    """
    
    def __init__(self):
        self.planner = QueryPlannerAgent()
        self.structured_agent = StructuredDataAgent()
        self.structural_agent = StructuralRelationshipAgent()
        self.geometry_agent = GeometrySemanticAgent()
        self._connected = False
    
    async def connect(self):
        """连接所有智能体"""
        if not self._connected:
            await self.planner.connect()
            await self.structured_agent.connect()
            await self.structural_agent.connect()
            await self.geometry_agent.connect()
            self._connected = True
            print("多智能体查询系统已连接")
    
    async def disconnect(self):
        """断开所有智能体连接"""
        if self._connected:
            await self.planner.disconnect()
            await self.structured_agent.disconnect()
            await self.structural_agent.disconnect()
            await self.geometry_agent.disconnect()
            self._connected = False
            print("多智能体查询系统已断开连接")
    
    async def execute_query(self, query_text: str, shape_vector: Optional[List[float]] = None, top_k: int = 10) -> Dict[str, Any]:
        """
        执行完整的多智能体查询
        
        Args:
            query_text: 用户查询文本
            shape_vector: 可选的形状向量
            top_k: 返回结果数量
            
        Returns:
            Dict: 包含查询计划和执行结果的字典
        """
        if not self._connected:
            await self.connect()
        
        # 1. 生成查询计划
        print(f"🔍 分析查询: {query_text}")
        query_plan = await self.planner.get_query_plan_only(query_text, shape_vector, top_k)
        
        if not query_plan:
            return {
                "success": False,
                "error": "查询计划生成失败",
                "query_plan": None,
                "results": []
            }
        
        print(f"📋 生成查询计划，包含 {len(query_plan.steps)} 个步骤")
        
        # 2. 执行查询步骤
        step_results = {}
        final_results = []
        
        for step in query_plan.steps:
            print(f"⚙️  执行步骤: {step['step_name']} ({step['agent']})")
            
            try:
                # 处理依赖关系
                id_list = None
                if step.get('depends_on') and step['depends_on'] in step_results:
                    # 从前面步骤的结果中提取ID列表
                    prev_result = step_results[step['depends_on']]
                    if prev_result.status == 'success':
                        id_list = [item.uuid for item in prev_result.results]
                        print(f"   使用前面步骤的 {len(id_list)} 个结果作为过滤条件")
                
                # 执行当前步骤
                result = await self._execute_step(step, id_list)
                step_results[step['id']] = result
                
                if result.status == 'success':
                    print(f"   ✓ 步骤完成，找到 {len(result.results)} 个结果")
                    final_results.extend(result.results)
                else:
                    print(f"   ✗ 步骤失败: {result.error_message}")
                
            except Exception as e:
                print(f"   ✗ 步骤执行异常: {e}")
        
        # 3. 结果融合和排序
        if final_results:
            final_results = self._merge_and_rank_results(final_results, query_plan.result_fusion)
            print(f"🎯 最终返回 {len(final_results)} 个结果")
        
        return {
            "success": True,
            "query_plan": query_plan.model_dump(),
            "step_results": {k: v.model_dump() for k, v in step_results.items()},
            "final_results": [item.model_dump() for item in final_results],
            "total_results": len(final_results)
        }
    
    async def _execute_step(self, step: Dict[str, Any], id_list: Optional[List[str]] = None) -> QueryResult:
        """
        执行单个查询步骤
        
        Args:
            step: 查询步骤定义
            id_list: 可选的ID过滤列表
            
        Returns:
            QueryResult: 步骤执行结果
        """
        agent_type = step['agent']
        parameters = step['parameters'].copy()
        
        # 添加ID过滤条件
        if id_list:
            parameters['id_list'] = id_list
        
        # 创建任务并执行
        task_id = str(uuid.uuid4())
        
        if agent_type == 'StructuredDataAgent':
            task = UnifiedStructuredDataTask(
                task_id=task_id,
                **parameters
            )
            return await self.structured_agent.execute_task(task)
        
        elif agent_type == 'StructuralRelationshipAgent':
            task = UnifiedStructuralQueryTask(
                task_id=task_id,
                **parameters
            )
            return await self.structural_agent.execute_task(task)
        
        elif agent_type == 'GeometrySemanticAgent':
            task = UnifiedGeometrySemanticTask(
                task_id=task_id,
                **parameters
            )
            return await self.geometry_agent.execute_task(task)
        
        else:
            raise ValueError(f"未知的智能体类型: {agent_type}")
    
    def _merge_and_rank_results(self, results: List[SearchResultItem], fusion_strategy: Dict[str, Any]) -> List[SearchResultItem]:
        """
        合并和排序结果
        
        Args:
            results: 搜索结果列表
            fusion_strategy: 融合策略
            
        Returns:
            List[SearchResultItem]: 排序后的结果列表
        """
        # 去重（基于UUID）
        unique_results = {}
        for item in results:
            if item.uuid not in unique_results:
                unique_results[item.uuid] = item
            else:
                # 如果有重复，保留相似度更高的
                existing = unique_results[item.uuid]
                if (item.similarity_score or 0) > (existing.similarity_score or 0):
                    unique_results[item.uuid] = item
        
        merged_results = list(unique_results.values())
        
        # 根据融合策略排序
        ranking_criteria = fusion_strategy.get('ranking_criteria', [])
        
        for criterion in reversed(ranking_criteria):  # 反向应用排序条件
            field = criterion['field']
            reverse = criterion['direction'] == 'desc'
            
            if field == 'similarity_score':
                merged_results.sort(key=lambda x: x.similarity_score or 0, reverse=reverse)
            elif field == 'rank':
                merged_results.sort(key=lambda x: x.rank, reverse=reverse)
        
        return merged_results


async def test_simple_semantic_query():
    """测试简单语义查询"""
    print("\n" + "="*60)
    print("测试1: 简单语义查询")
    print("="*60)
    
    system = MultiAgentQuerySystem()
    
    try:
        result = await system.execute_query(
            query_text="找一些圆形的零件",
            top_k=5
        )
        
        if result['success']:
            print("✓ 查询执行成功")
            print(f"查询计划步骤数: {len(result['query_plan']['steps'])}")
            print(f"最终结果数: {result['total_results']}")
            return True
        else:
            print(f"✗ 查询失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False
    finally:
        await system.disconnect()


async def test_complex_multi_constraint_query():
    """测试复杂多约束查询"""
    print("\n" + "="*60)
    print("测试2: 复杂多约束查询")
    print("="*60)
    
    system = MultiAgentQuerySystem()
    
    try:
        result = await system.execute_query(
            query_text="给我找跟上传的模型差不多的泵阀零件，材料得用304不锈钢，而且要直接用法兰接到离心泵",
            shape_vector=[0.1] * 768,  # 模拟形状向量
            top_k=10
        )
        
        if result['success']:
            print("✓ 查询执行成功")
            print(f"查询计划步骤数: {len(result['query_plan']['steps'])}")
            print(f"最终结果数: {result['total_results']}")
            
            # 显示查询计划
            print("\n查询计划:")
            for i, step in enumerate(result['query_plan']['steps'], 1):
                print(f"  步骤{i}: {step['step_name']} ({step['agent']})")
                print(f"    描述: {step['description']}")
            
            return True
        else:
            print(f"✗ 查询失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False
    finally:
        await system.disconnect()


async def test_structural_relationship_query():
    """测试结构关系查询"""
    print("\n" + "="*60)
    print("测试3: 结构关系查询")
    print("="*60)
    
    system = MultiAgentQuerySystem()
    
    try:
        result = await system.execute_query(
            query_text="查一批布局跟这组齿轮差不多、传动比大约4比1的减速机，输入轴得用键跟电机轴联接",
            top_k=8
        )
        
        if result['success']:
            print("✓ 查询执行成功")
            print(f"查询计划步骤数: {len(result['query_plan']['steps'])}")
            print(f"最终结果数: {result['total_results']}")
            
            # 检查是否包含结构关系查询
            has_structural = any(
                step['agent'] == 'StructuralRelationshipAgent' 
                for step in result['query_plan']['steps']
            )
            
            if has_structural:
                print("✓ 包含结构关系查询步骤")
            else:
                print("⚠ 未检测到结构关系查询步骤")
            
            return True
        else:
            print(f"✗ 查询失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False
    finally:
        await system.disconnect()


async def main():
    """主测试函数"""
    print("🚀 开始测试多智能体查询系统（包含查询规划智能体）")
    
    test_results = []
    
    # 测试简单语义查询
    try:
        result = await test_simple_semantic_query()
        test_results.append(("简单语义查询", result))
    except Exception as e:
        print(f"简单语义查询测试异常: {e}")
        test_results.append(("简单语义查询", False))
    
    # 测试复杂多约束查询
    try:
        result = await test_complex_multi_constraint_query()
        test_results.append(("复杂多约束查询", result))
    except Exception as e:
        print(f"复杂多约束查询测试异常: {e}")
        test_results.append(("复杂多约束查询", False))
    
    # 测试结构关系查询
    try:
        result = await test_structural_relationship_query()
        test_results.append(("结构关系查询", result))
    except Exception as e:
        print(f"结构关系查询测试异常: {e}")
        test_results.append(("结构关系查询", False))
    
    # 输出测试结果汇总
    print("\n" + "="*60)
    print("🎯 测试结果汇总")
    print("="*60)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总计: {success_count}/{len(test_results)} 个测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！多智能体查询系统功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    asyncio.run(main())
