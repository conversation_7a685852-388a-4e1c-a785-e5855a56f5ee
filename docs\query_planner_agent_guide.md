# 查询规划智能体使用指南

## 概述

查询规划智能体（QueryPlannerAgent）是多智能体查询系统的核心组件，负责分析用户的自然语言查询，识别查询意图和约束条件，生成结构化的查询计划，并协调三个专业查询智能体的工作。

## 核心功能

### 1. 查询意图分析

查询规划智能体能够智能分析用户查询，识别以下要素：

- **主要查询对象**：零件、装配体、组件等
- **形状几何约束**：形状描述、几何特征等
- **属性约束**：材料、质量、尺寸等物理属性
- **语义描述**：功能描述、用途说明等
- **结构关系约束**：装配关系、连接方式等

### 2. 查询计划生成

基于意图分析结果，生成包含以下内容的结构化查询计划：

- **查询步骤序列**：明确的执行顺序和依赖关系
- **智能体分配**：为每个步骤分配合适的专业智能体
- **参数配置**：为每个智能体配置具体的查询参数
- **结果融合策略**：定义如何合并和排序最终结果

### 3. 智能体协调

协调三个专业查询智能体的工作：

- **StructuredDataAgent**：处理精确属性查询
- **StructuralRelationshipAgent**：处理结构关系查询
- **GeometrySemanticAgent**：处理语义和几何查询

## 使用方法

### 基础用法

```python
import asyncio
from src.agent.query_planner_agent import QueryPlannerAgent
from src.agent.data_models import QueryPlannerTask

async def basic_usage():
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 创建查询任务
        task = QueryPlannerTask(
            task_id="unique_task_id",
            query_text="找一些圆形的零件",
            top_k=10
        )
        
        # 执行查询规划
        result = await agent.execute_task(task)
        
        if result.status == 'success':
            query_plan = result.results[0].metadata
            print("查询计划:", query_plan)
        
    finally:
        await agent.disconnect()

asyncio.run(basic_usage())
```

### 复杂查询示例

```python
async def complex_query():
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 复杂多约束查询
        task = QueryPlannerTask(
            task_id="complex_task",
            query_text="给我找跟上传的模型差不多的泵阀零件，材料得用304不锈钢，而且要直接用法兰接到离心泵",
            shape_vector=[0.1] * 768,  # 形状向量
            top_k=15
        )
        
        result = await agent.execute_task(task)
        
        if result.status == 'success':
            query_plan = result.results[0].metadata
            
            # 查看查询步骤
            for step in query_plan['steps']:
                print(f"步骤: {step['step_name']}")
                print(f"智能体: {step['agent']}")
                print(f"描述: {step['description']}")
                print("---")
        
    finally:
        await agent.disconnect()
```

### 仅获取查询计划

```python
async def get_plan_only():
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 使用便捷方法仅获取查询计划
        query_plan = await agent.get_query_plan_only(
            query_text="在铝合金部件里，找形状跟这个模型相近的碰撞缓冲块",
            shape_vector=[0.2] * 768,
            top_k=12
        )
        
        if query_plan:
            print(f"计划ID: {query_plan.plan_id}")
            print(f"步骤数量: {len(query_plan.steps)}")
            print("意图分析:", query_plan.intent_analysis)
        
    finally:
        await agent.disconnect()
```

## 查询计划结构

### 完整的查询计划示例

```json
{
  "plan_id": "6efef693-1fad-4eee-ade8-9a39cf9e9711",
  "query_text": "给我找一个包含Cabos和Shell的装配体，用于流体流量的测量和监控，并且重量要超过100",
  "intent_analysis": {
    "main_query_object": "装配体",
    "shape_geometry_constraints": [],
    "attribute_constraints": {
      "mass": ">100"
    },
    "semantic_descriptions": [
      "用于流体流量的测量和监控"
    ],
    "structural_relationship_constraints": [
      "包含Cabos",
      "包含Shell"
    ],
    "has_shape_vector": false
  },
  "steps": [
    {
      "id": "step1",
      "step_name": "structural_query",
      "agent": "StructuralRelationshipAgent",
      "parameters": {
        "query_text": "包含Cabos和Shell的装配体"
      },
      "description": "首先查询包含指定组件的装配体",
      "depends_on": null
    },
    {
      "id": "step2",
      "step_name": "attribute_filter",
      "agent": "StructuredDataAgent",
      "parameters": {
        "query_text": "质量超过100的装配体",
        "input_ids": "来自structural_query的结果"
      },
      "description": "对结构查询结果进行质量筛选",
      "depends_on": "structural_query"
    },
    {
      "id": "step3",
      "step_name": "semantic_filter",
      "agent": "GeometrySemanticAgent",
      "parameters": {
        "query_text": "用于流体流量的测量和监控",
        "input_ids": "来自attribute_filter的结果",
        "shape_weight": 0.2,
        "top_k": 10
      },
      "description": "对筛选后的结果进行功能匹配",
      "depends_on": "attribute_filter"
    }
  ],
  "result_fusion": {
    "merge_method": "intersection",
    "ranking_criteria": [
      {
        "field": "mass",
        "direction": "desc"
      },
      {
        "field": "semantic_similarity",
        "direction": "desc"
      }
    ],
    "description": "先按质量降序，再按语义相似度降序"
  }
}
```

## 查询类型支持

### 1. 简单语义查询
- **示例**：`"找一些圆形的零件"`
- **特点**：单一语义描述，使用GeometrySemanticAgent

### 2. 属性过滤查询
- **示例**：`"帮我找壁厚不超过2毫米、能支撑5公斤以上的部件"`
- **特点**：包含精确属性约束，使用StructuredDataAgent

### 3. 结构关系查询
- **示例**：`"查找包含轴承的装配体"`
- **特点**：涉及组件关系，使用StructuralRelationshipAgent

### 4. 混合查询
- **示例**：`"给我找跟上传的模型差不多的泵阀零件，材料得用304不锈钢"`
- **特点**：包含多种约束，需要多个智能体协作

### 5. 形状相似查询
- **示例**：提供shape_vector参数
- **特点**：基于形状向量的相似性搜索

## 最佳实践

### 1. 查询文本编写建议

- **明确查询对象**：明确指出要查找的是零件、装配体还是组件
- **详细描述约束**：尽可能详细地描述材料、尺寸、功能等约束条件
- **使用专业术语**：使用准确的工程术语有助于提高识别精度

### 2. 参数配置建议

- **top_k设置**：根据实际需求设置合理的结果数量
- **形状向量**：如果有形状要求，提供768维的形状向量
- **超时处理**：为复杂查询设置合理的超时时间

### 3. 错误处理

```python
async def robust_query():
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        task = QueryPlannerTask(
            task_id="robust_task",
            query_text="查询文本",
            top_k=10
        )
        
        result = await agent.execute_task(task)
        
        if result.status == 'success':
            # 处理成功结果
            query_plan = result.results[0].metadata
            return query_plan
        else:
            # 处理失败情况
            print(f"查询规划失败: {result.error_message}")
            return None
            
    except Exception as e:
        print(f"执行异常: {e}")
        return None
    finally:
        await agent.disconnect()
```

## 性能优化

### 1. 连接管理
- 对于批量查询，保持连接状态避免重复连接
- 及时断开连接释放资源

### 2. 缓存策略
- 对于相似查询，可以考虑缓存查询计划
- 避免重复的意图分析和计划生成

### 3. 并发控制
- 合理控制并发查询数量
- 避免过多的并发请求影响LLM服务

## 故障排除

### 常见问题

1. **LLM连接失败**
   - 检查LLM服务配置
   - 验证API密钥和网络连接

2. **查询计划生成失败**
   - 检查查询文本是否过于复杂或模糊
   - 尝试简化查询表达

3. **意图分析不准确**
   - 使用更明确的专业术语
   - 提供更详细的约束条件

### 调试技巧

- 启用详细日志记录
- 检查中间步骤的输出
- 验证LLM响应的JSON格式

## 扩展开发

### 自定义意图分析

可以通过修改`_build_intent_analysis_prompt`方法来自定义意图分析逻辑：

```python
def _build_intent_analysis_prompt(self) -> str:
    # 自定义系统提示词
    return "自定义的意图分析提示词..."
```

### 自定义查询步骤生成

可以通过修改`_build_step_generation_prompt`方法来自定义步骤生成逻辑：

```python
def _build_step_generation_prompt(self) -> str:
    # 自定义步骤生成提示词
    return "自定义的步骤生成提示词..."
```
