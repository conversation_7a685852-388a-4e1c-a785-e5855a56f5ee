#!/usr/bin/env python3
"""
测试查询规划智能体
验证QueryPlannerAgent的功能，包括各种复杂查询场景的测试
"""

import asyncio
import uuid
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agent.query_planner_agent import QueryPlannerAgent
from src.agent.data_models import QueryPlannerTask


async def test_basic_query_planning():
    """测试基础查询规划功能"""
    print("\n=== 测试基础查询规划功能 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 测试简单的语义查询
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="找一些圆形的零件",
            top_k=10
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        
        if result.status == 'success' and result.results:
            query_plan = result.results[0].metadata
            print("查询计划:")
            print(json.dumps(query_plan, ensure_ascii=False, indent=2))
            return True
        else:
            print(f"测试失败: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_complex_multi_constraint_query():
    """测试复杂多约束查询"""
    print("\n=== 测试复杂多约束查询 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 测试包含多种约束的复杂查询
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="给我找跟上传的模型差不多的泵阀零件，材料得用304不锈钢，而且要直接用法兰接到离心泵",
            shape_vector=[0.1] * 768,  # 模拟形状向量
            top_k=15
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        
        if result.status == 'success' and result.results:
            query_plan = result.results[0].metadata
            print("查询计划:")
            print(json.dumps(query_plan, ensure_ascii=False, indent=2))
            
            # 验证查询计划的结构
            assert 'intent_analysis' in query_plan
            assert 'steps' in query_plan
            assert 'result_fusion' in query_plan
            assert len(query_plan['steps']) > 0
            
            print("✓ 查询计划结构验证通过")
            return True
        else:
            print(f"测试失败: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_structural_relationship_query():
    """测试结构关系查询"""
    print("\n=== 测试结构关系查询 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 测试结构关系查询
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="查一批布局跟这组齿轮差不多、传动比大约4比1的减速机，输入轴得用键跟电机轴联接",
            top_k=10
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        
        if result.status == 'success' and result.results:
            query_plan = result.results[0].metadata
            print("查询计划:")
            print(json.dumps(query_plan, ensure_ascii=False, indent=2))
            
            # 检查是否包含结构关系查询步骤
            steps = query_plan.get('steps', [])
            has_structural_step = any(
                step.get('agent') == 'StructuralRelationshipAgent' 
                for step in steps
            )
            
            if has_structural_step:
                print("✓ 包含结构关系查询步骤")
            else:
                print("⚠ 未检测到结构关系查询步骤")
            
            return True
        else:
            print(f"测试失败: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_attribute_filter_query():
    """测试属性过滤查询"""
    print("\n=== 测试属性过滤查询 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 测试属性过滤查询
        task = QueryPlannerTask(
            task_id=str(uuid.uuid4()),
            query_text="帮我找壁厚不超过2毫米、能支撑5公斤以上、具有缓冲功能的部件",
            top_k=20
        )
        
        print(f"执行任务: {task.query_text}")
        result = await agent.execute_task(task)
        
        print(f"任务状态: {result.status}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        
        if result.status == 'success' and result.results:
            query_plan = result.results[0].metadata
            print("查询计划:")
            print(json.dumps(query_plan, ensure_ascii=False, indent=2))
            
            # 检查是否包含属性过滤步骤
            steps = query_plan.get('steps', [])
            has_structured_step = any(
                step.get('agent') == 'StructuredDataAgent' 
                for step in steps
            )
            
            if has_structured_step:
                print("✓ 包含属性过滤查询步骤")
            else:
                print("⚠ 未检测到属性过滤查询步骤")
            
            return True
        else:
            print(f"测试失败: {result.error_message}")
            return False
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        await agent.disconnect()


async def test_get_query_plan_only():
    """测试仅获取查询计划功能"""
    print("\n=== 测试仅获取查询计划功能 ===")
    
    agent = QueryPlannerAgent()
    
    try:
        await agent.connect()
        
        # 使用便捷方法获取查询计划
        query_plan = await agent.get_query_plan_only(
            query_text="在铝合金部件里，找形状跟这个模型相近的碰撞缓冲块，要通过橡胶垫片连到车身侧梁",
            shape_vector=[0.2] * 768,
            top_k=12
        )
        
        if query_plan:
            print("查询计划生成成功:")
            print(f"计划ID: {query_plan.plan_id}")
            print(f"查询文本: {query_plan.query_text}")
            print(f"步骤数量: {len(query_plan.steps)}")
            print("意图分析:", json.dumps(query_plan.intent_analysis, ensure_ascii=False, indent=2))
            return True
        else:
            print("查询计划生成失败")
            return False
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    finally:
        await agent.disconnect()


async def main():
    """主测试函数"""
    print("开始测试查询规划智能体...")
    
    test_results = []
    
    # 测试基础查询规划
    try:
        result = await test_basic_query_planning()
        test_results.append(("基础查询规划", result))
    except Exception as e:
        print(f"基础查询规划测试异常: {e}")
        test_results.append(("基础查询规划", False))
    
    # 测试复杂多约束查询
    try:
        result = await test_complex_multi_constraint_query()
        test_results.append(("复杂多约束查询", result))
    except Exception as e:
        print(f"复杂多约束查询测试异常: {e}")
        test_results.append(("复杂多约束查询", False))
    
    # 测试结构关系查询
    try:
        result = await test_structural_relationship_query()
        test_results.append(("结构关系查询", result))
    except Exception as e:
        print(f"结构关系查询测试异常: {e}")
        test_results.append(("结构关系查询", False))
    
    # 测试属性过滤查询
    try:
        result = await test_attribute_filter_query()
        test_results.append(("属性过滤查询", result))
    except Exception as e:
        print(f"属性过滤查询测试异常: {e}")
        test_results.append(("属性过滤查询", False))
    
    # 测试仅获取查询计划
    try:
        result = await test_get_query_plan_only()
        test_results.append(("仅获取查询计划", result))
    except Exception as e:
        print(f"仅获取查询计划测试异常: {e}")
        test_results.append(("仅获取查询计划", False))
    
    # 输出测试结果汇总
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(test_results)} 个测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！查询规划智能体功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")


if __name__ == "__main__":
    asyncio.run(main())
