import json, textwrap
import logging
import os
from typing import Dict, Any, List, Optional, Tuple
from src.data_formats import CADModelData, AssemblyData, SubAssemblyData, PartData, FeatureData
from src.data_converters import Fusion360Converter
from dataclasses import dataclass, field
from src.models.clip import CLIPFeatureExtractor
import torch
from PIL import Image
from src.utils.cad_utils import measure_lwh_obb, analyze_holes

@dataclass
class BodyData:
    body_id: str
    name: str
    type: str
    physical_properties: Dict[str, Any] = field(default_factory=dict)
    material: Optional[Dict[str, Any]] = None
    appearance: Optional[Dict[str, Any]] = None
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ComponentData:
    component_id: str
    name: str
    type: str
    part_number: Optional[str] = None
    bodies: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class OccurrenceData:
    occurrence_id: str
    name: str
    component: str
    is_grounded: bool = False
    is_visible: bool = True
    physical_properties: Dict[str, Any] = field(default_factory=dict)
    transform: Dict[str, Any] = field(default_factory=dict)
    properties: Dict[str, Any] = field(default_factory=dict)

class Fusion360Extractor:
    """
    Fusion360数据提取器，用于从Fusion360导出的JSON文件中提取更详细的信息.
    
    Fusion360 JSON格式说明见datasets/fusion360.md,主要包含以下部分：
    - tree: 设计者定义的装配体层级结构
    - root: 设计者定义的装配体根组件
    - occurrences: 组件的实例，包含位置和方向信息
    - components: 组件，包含实体或其他组件
    - bodies: 底层3D形状几何体
    - joints: 约束实例间相对位置的关节
    - as_built_joints: 保持空间位置不变的关节
    - contacts: 不同实体之间的接触面
    - holes: 孔特征列表
    - properties: 装配体的统计信息和元数据

    Fusion360的json处理逻辑如下:
    1.为Body,  Component 和 Occurrence 分别创建数据结构. Component中包含Body对象,Occurrence中包含Component对象
    2.在解析json时先提取bodies,再提取component,最后提取Occurrence
    3. 新建AssemblyData装配体, 将root节点的component转换为PartData零件或SubAssemblyData子装配体(根据body的数量判断,若body数量大于1,则转换为子装配体,否则转换为零件)
    4. 处理tree下面的节点，根据层次结构依次加入到装配体中。处理逻辑如下：
        （1）对于第一层的节点，如果该节点没有子节点,则根据occurrence的component的body数量判断,若body数量大于1,则转换为子装配体,若body数量等于1,则转换为零件,并添加到装配体中,若body数量为0,则不添加
        （2）如果该节点有子节点,则将其转换为子装配体,并添加到装配体中. 此外,还要递归处理子节点,并将子节点添加到其父节点中

    """
    def __init__(self, json_file: str, clip_extractor=None, extract_shape_embedding: bool = True):
        self.json_file = json_file
        self.extract_shape_embedding = extract_shape_embedding
        with open(json_file, "r", encoding="utf-8") as f:
            self.json_data = json.load(f)
        self.bodies = self.json_data.get("bodies", {})
        self.components = self.json_data.get("components", {})
        self.occurrences = self.json_data.get("occurrences", {})
        self.holes = self.json_data.get("holes", [])
        self.properties = self.json_data.get("properties", {})
        self.tree = self.json_data.get("tree", {}).get("root", {})
        self.root = self.json_data.get("root", {})
        # 建立body/component/occurrence的映射
        self.body_map = self._parse_bodies()
        self.component_map = self._parse_components()
        self.occurrence_map = self._parse_occurrences()
        self.hole_map = self._parse_holes()
        # 根据参数决定是否初始化CLIP特征提取器
        self.clip_extractor = None
        if self.extract_shape_embedding:
            if clip_extractor is None:
                from src.models.clip import CLIPFeatureExtractor
                self.clip_extractor = CLIPFeatureExtractor()
            else:
                self.clip_extractor = clip_extractor

    def convert(self) -> CADModelData:
        # 1. 构建顶层装配体 AssemblyData
        assembly_id = os.path.basename(os.path.dirname(self.json_file))
        assembly_name = self.properties.get("name", assembly_id)
        area = self.properties.get("area", 0.0)
        volume = self.properties.get("volume", 0.0)
        density = self.properties.get("density", 0.0)
        mass = self.properties.get("mass", 0.0)
        industry = None
        category = None
        industries = self.properties.get("industries", [])
        categories = self.properties.get("categories", [])
        if industries:
            industry = industries[0]
        if categories:
            category = categories[0]
        parts = []
        subassemblies = []
        # 先单独处理root的component
        root_comp_id = self.root.get("component", None)
        if root_comp_id:
            root_comp = self.component_map.get(root_comp_id)
            if root_comp:
                body_ids = root_comp.bodies
                part_number = root_comp.part_number if hasattr(root_comp, 'part_number') and root_comp.part_number else root_comp.name
                if len(body_ids) > 1:
                    root_parts = [self._build_part(bid, None, self.body_map[bid].name) for bid in body_ids if self._build_part(bid, None, self.body_map[bid].name)]
                    shape_embedding = self._extract_shape_embedding(root_comp_id)
                    subassembly = SubAssemblyData(
                        subassembly_id=root_comp_id,
                        name=part_number,
                        area=0.0,
                        volume=0.0,
                        density=0.0,
                        mass=0.0,
                        parts=root_parts,
                        subassemblies=[],
                        properties=root_comp.properties,
                        shape_embedding=shape_embedding
                    )
                    subassemblies.append(subassembly)
                elif len(body_ids) == 1:
                    part = self._build_part(body_ids[0], None, part_number)
                    if part:
                        parts.append(part)
                # bodies==0 不处理
        # 再递归处理tree
        for occ_id, children in self.tree.items():
            node = self._parse_tree(occ_id, children)
            if isinstance(node, PartData):
                parts.append(node)
            elif isinstance(node, SubAssemblyData):
                subassemblies.append(node)
        # 提取根装配体形状嵌入
        shape_embedding = self._extract_shape_embedding("assembly")
        # 计算装配体的长宽高, 装配体的路径与json_file同目录，文件名为assembly.obj
        assembly_obj_path = os.path.join(os.path.dirname(self.json_file), "assembly.obj")
        length = width = height = 0.0
        if os.path.exists(assembly_obj_path):
            try:
                length, width, height = measure_lwh_obb(assembly_obj_path)
            except Exception as e:
                logging.warning(f"计算装配体OBB尺寸失败: {assembly_obj_path}, {e}")
        else:
            logging.warning(f"装配体OBJ文件不存在: {assembly_obj_path}")

        # 计算装配体的零件数量（递归统计所有子装配体的零件）
        part_count = self.count_all_parts(parts, subassemblies)

        # 获取装配体的文本描述和名称
        description_path = os.path.join(os.path.dirname(self.json_file), "description.json")
        description = f"a model of a {assembly_name}"
        if os.path.exists(description_path):
            try:
                with open(description_path, "r", encoding="utf-8") as f:
                    desc_data = json.load(f)
                    # 如果description.json中包含assembly_name字段，则优先使用它
                    if "assembly_name" in desc_data:
                        assembly_name = desc_data["assembly_name"]
                    
                    # 提取重点信息再拼成自然语言，减少 token 数
                    component_desc = "组件包括："
                    for comp in desc_data["components"]:
                        component_desc += f'{comp["name"]}（功能：{comp["feature"]}），'

                    component_desc = component_desc.rstrip("，") + "。"
                    description = desc_data["description"] + "。材料与工艺：" + desc_data["materials and techniques"] + "。" + component_desc
            except Exception as e:
                logging.warning(f"读取装配体描述文件失败: {description_path}, {e}")
        else:
            logging.warning(f"装配体描述文件不存在: {description_path}")

        assembly = AssemblyData(
            assembly_id=assembly_id,
            name=assembly_name,
            length=length,
            width=width,
            height=height,
            area=area,
            volume=volume,
            density=density,
            mass=mass,
            industry=industry,
            category=category,
            part_count=part_count,
            parts=parts,
            subassemblies=subassemblies,
            properties=self.properties,
            shape_embedding=shape_embedding,
            description=description
        )
        return CADModelData(assembly=assembly, source_format="fusion360", metadata={})

    def _parse_bodies(self) -> Dict[str, BodyData]:
        body_map = {}
        for body_id, body in self.bodies.items():
            body_map[body_id] = BodyData(
                body_id=body_id,
                name=body.get("name", ""),
                type=body.get("type", ""),
                physical_properties=body.get("physical_properties", {}),
                material=body.get("material"),
                appearance=body.get("appearance"),
                properties=body
            )
        return body_map

    def _parse_components(self) -> Dict[str, ComponentData]:
        comp_map = {}
        for comp_id, comp in self.components.items():
            comp_map[comp_id] = ComponentData(
                component_id=comp_id,
                name=comp.get("name", ""),
                type=comp.get("type", ""),
                part_number=comp.get("part_number"),
                bodies=comp.get("bodies", []),
                properties=comp
            )
        return comp_map

    def _parse_occurrences(self) -> Dict[str, OccurrenceData]:
        occ_map = {}
        for occ_id, occ in self.occurrences.items():
            occ_map[occ_id] = OccurrenceData(
                occurrence_id=occ_id,
                name=occ.get("name", ""),
                component=occ.get("component", ""),
                is_grounded=occ.get("is_grounded", False),
                is_visible=occ.get("is_visible", True),
                physical_properties=occ.get("physical_properties", {}),
                transform=occ.get("transform", {}),
                properties=occ
            )
        return occ_map

    def _parse_holes(self) -> Dict[str, List[Dict[str, Any]]]:
        # 将holes按body分组
        hole_map = {}
        for hole in self.holes:
            body_id = hole.get("body")
            if body_id:
                hole_map.setdefault(body_id, []).append(hole)
        return hole_map

    def _parse_tree(self, occ_id: str, children: Dict[str, Any]) -> Optional[Any]:
        occ = self.occurrence_map.get(occ_id)
        if not occ:
            return None
        comp_id = occ.component
        comp = self.component_map.get(comp_id)
        if not comp:
            return None
        body_ids = comp.bodies
        part_number = comp.part_number if hasattr(comp, 'part_number') and comp.part_number else comp.name
        # 递归处理子节点
        sub_nodes = []
        for child_occ_id, child_children in children.items():
            child_node = self._parse_tree(child_occ_id, child_children)
            if child_node:
                sub_nodes.append(child_node)
        # 判断当前节点类型
        if len(body_ids) == 0 and not sub_nodes:
            return None  # 空节点
        # TODO 如果len(body_ids) == 0 ，并且只有一个子节点，并且该子节点的len(body_ids) == 1，则视为零件
        elif len(body_ids) == 0 and len(sub_nodes) == 1 and isinstance(sub_nodes[0], PartData):
            # 如果当前节点没有body，并且只有一个子节点，且该子节点是零件，则直接将该子零件返回，但使用当前节点的名称
            part = sub_nodes[0]
            # 创建一个新的零件，使用当前节点的名称但保留子节点的其他属性
            return PartData(
                part_id=part.part_id,
                name=part_number,  # 使用当前节点的名称
                area=part.area,
                volume=part.volume,
                density=part.density,
                mass=part.mass,
                material=part.material,
                features=part.features,
                properties=part.properties,
                shape_embedding=part.shape_embedding
            )
        elif len(body_ids) == 1 and not sub_nodes:
            # 只有一个body且无子节点，视为零件
            part = self._build_part(body_ids[0], occ_id, part_number)
            return part
        else:
            # 有多个body或有子节点，视为子装配体
            parts = []
            subassemblies = []
            # 先将body转为零件，零件名用body的name
            for body_id in body_ids:
                part = self._build_part(body_id, occ_id, self.body_map[body_id].name)
                if part:
                    parts.append(part)
            # 子节点递归
            for node in sub_nodes:
                if isinstance(node, PartData):
                    parts.append(node)
                elif isinstance(node, SubAssemblyData):
                    subassemblies.append(node)
            # 提取子装配体形状嵌入
            shape_embedding = self._extract_shape_embedding(occ_id)
            subassembly = SubAssemblyData(
                subassembly_id=occ_id,
                name=part_number,
                area=0.0,
                volume=0.0,
                density=0.0,
                mass=0.0,
                parts=parts,
                subassemblies=subassemblies,
                properties={},
                shape_embedding=shape_embedding
            )
            return subassembly

    def _build_part(self, body_id: str, occ_id: str, part_name: str = None) -> PartData:
        body = self.body_map.get(body_id, None)
        if not body:
            return None
        name = part_name if part_name else body.name
        physical = body.physical_properties
        area = physical.get("area", 0.0)
        volume = physical.get("volume", 0.0)
        density = physical.get("density", 0.0)
        mass = physical.get("mass", 0.0)
        material = None
        if body.material:
            material = body.material.get("name")
        # 特征（目前只处理holes）
        features = []
        for hole in self.hole_map.get(body_id, []):
            feature = FeatureData(
                feature_id=f"hole_{body_id}_{hole.get('type','hole')}",
                name=hole.get("name", "hole"),
                type=hole.get("type", "hole"),
                length=hole.get("length", 0.0),
                diameter=hole.get("diameter", 0.0),
                properties=hole
            )
            features.append(feature)
        # 提取形状嵌入
        shape_embedding = self._extract_shape_embedding(body_id)
        # 计算零件的长宽高, 零件路径与json_file同目录，文件名为{body_id}.obj
        obj_path = os.path.join(os.path.dirname(self.json_file), f"{body_id}.obj")
        length = width = height = 0.0
        if os.path.exists(obj_path):
            try:
                length, width, height = measure_lwh_obb(obj_path)
            except Exception as e:
                logging.warning(f"计算OBB尺寸失败: {obj_path}, {e}")
        else:
            logging.warning(f"OBJ文件不存在: {obj_path}")

        # 计算孔特征数量、直径均值/方差、深度均值/方差
        hole_count, hole_diameter_mean, hole_diameter_std, hole_depth_mean, hole_depth_std = analyze_holes(features)

        # 获取零件的文本描述
        description = f"a model of a {name}"
        # 添加材质信息
        if material:
            description += f" made of {material}"
        # 添加孔信息
        if hole_count > 0:
            description += f" with {hole_count} holes"

        return PartData(
            part_id=body_id,
            name=name,
            length=length,
            width=width,
            height=height,
            area=area,
            volume=volume,
            density=density,
            mass=mass,
            material=material,
            hole_count=hole_count,
            hole_diameter_mean=hole_diameter_mean,
            hole_diameter_std=hole_diameter_std,
            hole_depth_mean=hole_depth_mean,
            hole_depth_std=hole_depth_std,
            features=features,
            properties=body.properties,
            shape_embedding=shape_embedding,
            description=description
        )
        
    def _extract_shape_embedding(self, node_id: str) -> Optional[torch.Tensor]:
        """
        提取节点对应图片的形状嵌入
        
        Args:
            node_id: 节点ID
            
        Returns:
            提取的形状嵌入向量，如果图片不存在则返回None
        """
        # 如果不需要提取形状嵌入，直接返回None
        if not self.extract_shape_embedding:
            return None
            
        # 获取json文件所在的目录
        json_dir = os.path.dirname(self.json_file)
        image_path = os.path.join(json_dir, f"{node_id}.png")
        try:
            if os.path.exists(image_path):
                image = self.clip_extractor.preprocess(Image.open(image_path)).unsqueeze(0)
                with torch.no_grad():
                    embedding = self.clip_extractor(image)
                return embedding.squeeze(0).cpu().numpy()
            else:
                logging.warning(f"图片不存在: {image_path}")
                return None
        except Exception as e:
            logging.error(f"提取形状嵌入失败: {e}")
            return None

    # 递归统计所有零件数量
    def count_all_parts(self, parts, subassemblies):
        count = len(parts)
        for sub in subassemblies:
            count += self.count_all_parts(sub.parts, sub.subassemblies)
        return count
    